import React, { useState, useEffect } from "react";
import useCheckMobileScreen from "@/hooks/useCheckMobileScreen";
import { ToastBar, Toaster, toast } from "react-hot-toast";
import { X, CheckCircle, AlertCircle, Info } from "lucide-react";
import { useLocation } from "react-router-dom";

// Helper function to get toast styling based on type
const getToastStyle = (type: string) => {
    switch (type) {
        case "success":
            return {
                icon: CheckCircle,
                iconColor: "text-green-200",
                gradient: "from-green-950 via-green-900 to-emerald-950",
                borderColor: "border-green-400/50",
                accentGradient: "from-green-500 to-emerald-400",
                glowColor: "shadow-green-500/30",
                overlayGradient: "from-green-500/10 via-transparent to-green-600/5",
            };
        case "error":
            return {
                icon: AlertCircle,
                iconColor: "text-red-200",
                gradient: "from-red-950 via-red-900 to-rose-950",
                borderColor: "border-red-400/50",
                accentGradient: "from-red-500 to-rose-400",
                glowColor: "shadow-red-500/30",
                overlayGradient: "from-red-500/10 via-transparent to-red-600/5",
            };
        case "loading":
            return {
                icon: Info,
                iconColor: "text-blue-200",
                gradient: "from-blue-950 via-blue-900 to-indigo-950",
                borderColor: "border-blue-400/50",
                accentGradient: "from-blue-500 to-indigo-400",
                glowColor: "shadow-blue-500/30",
                overlayGradient: "from-blue-500/10 via-transparent to-blue-600/5",
            };
        default:
            return {
                icon: Info,
                iconColor: "text-purple-200",
                gradient: "from-purple-950 via-purple-900 to-violet-950",
                borderColor: "border-purple-400/50",
                accentGradient: "from-purple-500 to-violet-400",
                glowColor: "shadow-purple-500/30",
                overlayGradient: "from-purple-500/10 via-transparent to-purple-600/5",
            };
    }
};

const ToastManager = () => {
    const isMobile = useCheckMobileScreen();
    const location = useLocation();
    const isChatPage = location.pathname === "/chat";
    const [windowWidth, setWindowWidth] = useState(window.innerWidth);

    // Handle window resize to update toast positioning
    useEffect(() => {
        const handleResize = () => setWindowWidth(window.innerWidth);
        window.addEventListener("resize", handleResize);
        return () => window.removeEventListener("resize", handleResize);
    }, []);

    let containerStyle: React.CSSProperties = {};
    if (isMobile) {
        containerStyle = {
            top: 65,
        };
    } else {
        // Position toasts in the top right of the main content container
        // Account for:
        // - Navbar height (~90px) + PageBanner
        // - Chatbox width (21.5vw on xl, 25.51vw on 2xl screens) when visible
        // - Some padding from the edge
        containerStyle = {
            top: 110, // Below navbar + page banner
        };

        // If chatbox is visible (not on chat page), account for its width
        if (!isChatPage) {
            containerStyle.right = "calc(21.5vw + 1rem)"; // Chatbox width + padding for xl screens

            // For 2xl screens, adjust for larger chatbox width
            if (windowWidth >= 1536) {
                // 2xl breakpoint
                containerStyle.right = "calc(25.51vw + 1rem)";
            }
        } else {
            // On chat page, chatbox is hidden, so position closer to right edge
            containerStyle.right = "1rem";
        }
    }

    return (
        <Toaster
            position={isMobile ? "top-center" : "top-right"}
            containerStyle={containerStyle}
            toastOptions={{
                duration: 4000,
                style: {
                    background: "transparent",
                    border: "none",
                    boxShadow: "none",
                    padding: 0,
                    maxWidth: isMobile ? "20rem" : "24rem",
                },
            }}
        >
            {(t) => (
                <ToastBar toast={t}>
                    {({ message }) => {
                        const toastStyle = getToastStyle(t.type);
                        const IconComponent = toastStyle.icon;

                        return (
                            <div
                                className={`
                                    relative overflow-hidden rounded-xl border backdrop-blur-sm
                                    bg-gradient-to-br ${toastStyle.gradient}
                                    ${toastStyle.borderColor} ${toastStyle.glowColor}
                                    shadow-2xl cursor-pointer group
                                    hover:scale-[1.02] transition-all duration-200 ease-out
                                    ${isMobile ? "max-w-sm mx-2" : "max-w-md"}
                                `}
                                onClick={() => toast.dismiss(t.id)}
                            >
                                {/* Enhanced overlay for depth and color */}
                                <div
                                    className={`absolute inset-0 bg-gradient-to-br ${toastStyle.overlayGradient} pointer-events-none`}
                                />
                                <div className="absolute inset-0 bg-gradient-to-br from-white/3 via-transparent to-black/20 pointer-events-none" />

                                {/* Content */}
                                <div className="relative flex items-center gap-3 p-4">
                                    {/* Icon */}
                                    <div className={`flex-shrink-0 ${toastStyle.iconColor} drop-shadow-sm`}>
                                        <IconComponent size={18} strokeWidth={2} />
                                    </div>

                                    {/* Message */}
                                    <div className="flex-1 min-w-0">
                                        <div className="text-white/95 text-sm font-medium leading-snug drop-shadow-md">
                                            {message}
                                        </div>
                                    </div>

                                    {/* Close button */}
                                    <button
                                        className="flex-shrink-0 text-white/60 hover:text-white transition-colors duration-200 p-1.5 rounded-lg hover:bg-white/10 group-hover:opacity-100 opacity-70"
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            toast.dismiss(t.id);
                                        }}
                                    >
                                        <X size={16} strokeWidth={1.5} />
                                    </button>
                                </div>

                                {/* Progress bar */}
                                <div className="absolute bottom-0 left-0 right-0 h-1 bg-black/20 overflow-hidden">
                                    <div
                                        className={`h-full bg-gradient-to-r ${toastStyle.accentGradient} transition-all duration-75 ease-linear`}
                                        style={{
                                            width: `${((4000 - (Date.now() - t.createdAt)) / 4000) * 100}%`,
                                            transition: t.visible ? "width 4000ms linear" : "none",
                                        }}
                                    />
                                </div>
                            </div>
                        );
                    }}
                </ToastBar>
            )}
        </Toaster>
    );
};

export default ToastManager;
